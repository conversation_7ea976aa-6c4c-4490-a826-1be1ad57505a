import { apiClient } from "../lib/api";
import type {
  ApiResponse,
  CreateProductVariantRequest,
  PaginatedResponse,
  ProductVariant,
  ProductVariantQueryParams,
  UpdateProductVariantRequest,
} from "../types/api";

export class ProductVariantService {
  private static readonly BASE_PATH = "/product-variants";

  // Ürün varyantlarını listele
  static async getProductVariants(
    params?: ProductVariantQueryParams
  ): Promise<ApiResponse<PaginatedResponse<ProductVariant>>> {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}?${queryString}`
      : this.BASE_PATH;

    return apiClient.get<PaginatedResponse<ProductVariant>>(endpoint);
  }

  // Belirli bir ürünün varyantlarını getir
  static async getProductVariantsByProduct(
    productId: string,
    params?: Omit<ProductVariantQueryParams, 'productId'>
  ): Promise<ApiResponse<PaginatedResponse<ProductVariant>>> {
    return this.getProductVariants({ ...params, productId });
  }

  // Tek varyant detayı
  static async getProductVariant(id: string): Promise<ApiResponse<ProductVariant>> {
    return apiClient.get<ProductVariant>(`${this.BASE_PATH}/${id}`);
  }

  // Yeni varyant oluştur
  static async createProductVariant(
    data: CreateProductVariantRequest
  ): Promise<ApiResponse<ProductVariant>> {
    return apiClient.post<ProductVariant>(this.BASE_PATH, data);
  }

  // Varyant güncelle
  static async updateProductVariant(
    id: string,
    data: UpdateProductVariantRequest
  ): Promise<ApiResponse<ProductVariant>> {
    return apiClient.put<ProductVariant>(`${this.BASE_PATH}/${id}`, data);
  }

  // Varyant sil
  static async deleteProductVariant(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_PATH}/${id}`);
  }

  // Varyant durumunu değiştir (aktif/pasif)
  static async toggleProductVariantStatus(
    id: string
  ): Promise<ApiResponse<ProductVariant>> {
    return apiClient.put<ProductVariant>(`${this.BASE_PATH}/${id}/toggle-status`, {});
  }

  // SKU benzersizlik kontrolü
  static async checkSkuUniqueness(
    productId: string,
    sku: string,
    excludeId?: string
  ): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ productId, sku });
    if (excludeId) {
      params.append("excludeId", excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(
      `${this.BASE_PATH}/check-sku?${params.toString()}`
    );
  }

  // Barkod benzersizlik kontrolü
  static async checkBarcodeUniqueness(
    productId: string,
    barcode: string,
    excludeId?: string
  ): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ productId, barcode });
    if (excludeId) {
      params.append("excludeId", excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(
      `${this.BASE_PATH}/check-barcode?${params.toString()}`
    );
  }

  // Varyant istatistikleri
  static async getProductVariantStats(
    productId?: string
  ): Promise<
    ApiResponse<{
      total: number;
      active: number;
      inactive: number;
      averagePrice: number;
      minPrice: number;
      maxPrice: number;
    }>
  > {
    const params = productId ? `?productId=${productId}` : "";
    return apiClient.get<{
      total: number;
      active: number;
      inactive: number;
      averagePrice: number;
      minPrice: number;
      maxPrice: number;
    }>(`${this.BASE_PATH}/stats${params}`);
  }

  // Toplu varyant oluşturma (örn: boyut seçenekleri için)
  static async createBulkProductVariants(
    productId: string,
    variants: Omit<CreateProductVariantRequest, 'productId'>[]
  ): Promise<ApiResponse<ProductVariant[]>> {
    return apiClient.post<ProductVariant[]>(`${this.BASE_PATH}/bulk`, {
      productId,
      variants,
    });
  }

  // Varyant sıralamasını güncelle
  static async updateProductVariantOrder(
    variantOrders: { id: string; displayOrder: number }[]
  ): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`${this.BASE_PATH}/reorder`, {
      variantOrders,
    });
  }

  // Varyant fiyat geçmişi
  static async getProductVariantPriceHistory(
    id: string
  ): Promise<
    ApiResponse<{
      id: string;
      price: number;
      costPrice?: number;
      updatedAt: string;
      updatedBy?: string;
    }[]>
  > {
    return apiClient.get<{
      id: string;
      price: number;
      costPrice?: number;
      updatedAt: string;
      updatedBy?: string;
    }[]>(`${this.BASE_PATH}/${id}/price-history`);
  }

  // Varyant stok durumu (eğer stok takibi varsa)
  static async getProductVariantStock(
    id: string
  ): Promise<
    ApiResponse<{
      currentStock: number;
      reservedStock: number;
      availableStock: number;
      criticalLevel?: number;
      isLowStock: boolean;
      isOutOfStock: boolean;
    }>
  > {
    return apiClient.get<{
      currentStock: number;
      reservedStock: number;
      availableStock: number;
      criticalLevel?: number;
      isLowStock: boolean;
      isOutOfStock: boolean;
    }>(`${this.BASE_PATH}/${id}/stock`);
  }

  // Varyant satış istatistikleri
  static async getProductVariantSalesStats(
    id: string,
    startDate?: string,
    endDate?: string
  ): Promise<
    ApiResponse<{
      totalSold: number;
      totalRevenue: number;
      averageOrderValue: number;
      topSellingDays: string[];
      salesTrend: { date: string; quantity: number; revenue: number }[];
    }>
  > {
    const params = new URLSearchParams();
    if (startDate) params.append("startDate", startDate);
    if (endDate) params.append("endDate", endDate);
    
    const queryString = params.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}/${id}/sales-stats?${queryString}`
      : `${this.BASE_PATH}/${id}/sales-stats`;

    return apiClient.get<{
      totalSold: number;
      totalRevenue: number;
      averageOrderValue: number;
      topSellingDays: string[];
      salesTrend: { date: string; quantity: number; revenue: number }[];
    }>(endpoint);
  }

  // Varyant kopyalama
  static async duplicateProductVariant(
    id: string,
    newName: string,
    newCode: string
  ): Promise<ApiResponse<ProductVariant>> {
    return apiClient.post<ProductVariant>(`${this.BASE_PATH}/${id}/duplicate`, {
      name: newName,
      code: newCode,
    });
  }

  // Varyant dışa aktarma
  static async exportProductVariants(
    params?: ProductVariantQueryParams,
    format: 'csv' | 'xlsx' = 'xlsx'
  ): Promise<ApiResponse<{ downloadUrl: string }>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }
    
    searchParams.append("format", format);
    
    return apiClient.get<{ downloadUrl: string }>(
      `${this.BASE_PATH}/export?${searchParams.toString()}`
    );
  }

  // Varyant içe aktarma
  static async importProductVariants(
    file: File,
    productId: string
  ): Promise<ApiResponse<{ 
    imported: number; 
    failed: number; 
    errors: string[] 
  }>> {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("productId", productId);

    return apiClient.post<{ 
      imported: number; 
      failed: number; 
      errors: string[] 
    }>(`${this.BASE_PATH}/import`, formData);
  }
}
