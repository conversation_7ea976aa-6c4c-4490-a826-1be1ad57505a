import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import { z } from "zod";
import { prisma } from "../index";

// JWT payload tipi
interface JWTPayload {
  id: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

// Fastify instance'ını auth plugin decorators ile genişlet
declare module "fastify" {
  interface FastifyInstance {
    authenticate: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    authorize: (
      roles: string[]
    ) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
}

// Validation şemaları
const createModifierSchema = z.object({
  groupId: z.string().cuid("Geçersiz modifiye grup ID"),
  name: z
    .string()
    .min(1, "Modifiye adı zorunludur")
    .max(100, "Modifiye adı en fazla 100 karakter olabilir"),
  price: z.number().min(0, "Fiyat negatif olamaz").default(0),
  maxQuantity: z
    .number()
    .int()
    .min(1, "Maksimum miktar en az 1 olmalıdır")
    .default(1),
  inventoryItemId: z.string().cuid("Geçersiz envanter öğesi ID").optional(),
  displayOrder: z
    .number()
    .int()
    .min(0, "Görüntüleme sırası negatif olamaz")
    .default(0),
  active: z.boolean().default(true),
});

const updateModifierSchema = createModifierSchema.partial().omit({
  groupId: true,
});

const querySchema = z.object({
  page: z
    .string()
    .transform(val => parseInt(val))
    .pipe(z.number().int().min(1))
    .default("1"),
  limit: z
    .string()
    .transform(val => parseInt(val))
    .pipe(z.number().int().min(1).max(100))
    .default("20"),
  search: z.string().optional(),
  groupId: z.string().cuid().optional(),
  active: z
    .string()
    .transform(val => val === "true")
    .pipe(z.boolean())
    .optional(),
  hasInventoryItem: z
    .string()
    .transform(val => val === "true")
    .pipe(z.boolean())
    .optional(),
  sortBy: z
    .enum(["name", "price", "displayOrder", "createdAt"])
    .default("displayOrder"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

export default async function (fastify: FastifyInstance) {
  // Modifiyeleri listele
  fastify.get(
    "/",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const query = querySchema.parse(request.query);

        // Where koşulları
        const where: any = {
          deletedAt: null,
          group: {
            deletedAt: null,
          },
        };

        // Arama
        if (query.search) {
          where.OR = [
            { name: { contains: query.search, mode: "insensitive" } },
            {
              group: { name: { contains: query.search, mode: "insensitive" } },
            },
          ];
        }

        // Filtreler
        if (query.groupId) {
          where.groupId = query.groupId;
        }

        if (query.active !== undefined) {
          where.active = query.active;
        }

        if (query.hasInventoryItem !== undefined) {
          if (query.hasInventoryItem) {
            where.inventoryItemId = { not: null };
          } else {
            where.inventoryItemId = null;
          }
        }

        // Sıralama
        const orderBy: any = {};
        orderBy[query.sortBy] = query.sortOrder;

        // Sayfalama hesaplamaları
        const skip = (query.page - 1) * query.limit;

        // Toplam kayıt sayısı
        const total = await prisma.modifier.count({ where });

        // Modifiyeleri getir
        const modifiers = await prisma.modifier.findMany({
          where,
          include: {
            group: {
              select: {
                id: true,
                name: true,
                minSelection: true,
                maxSelection: true,
                required: true,
                active: true,
              },
            },
            inventoryItem: {
              select: {
                id: true,
                name: true,
                code: true,
                currentStock: true,
                unit: true,
              },
            },
          },
          orderBy,
          skip,
          take: query.limit,
        });

        // Sayfalama bilgileri
        const totalPages = Math.ceil(total / query.limit);
        const hasNextPage = query.page < totalPages;
        const hasPrevPage = query.page > 1;

        return reply.send({
          success: true,
          data: {
            data: modifiers,
            pagination: {
              page: query.page,
              limit: query.limit,
              total,
              totalPages,
              hasNextPage,
              hasPrevPage,
            },
          },
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz sorgu parametreleri"
              : "Modifiyeler getirilirken bir hata oluştu",
        });
      }
    }
  );

  // Tek modifiye detayı
  fastify.get(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };

        const modifier = await prisma.modifier.findFirst({
          where: {
            id,
            deletedAt: null,
            group: {
              deletedAt: null,
            },
          },
          include: {
            group: {
              select: {
                id: true,
                name: true,
                description: true,
                minSelection: true,
                maxSelection: true,
                required: true,
                freeSelection: true,
                active: true,
              },
            },
            inventoryItem: {
              select: {
                id: true,
                name: true,
                code: true,
                currentStock: true,
                unit: true,
                criticalLevel: true,
                optimalLevel: true,
              },
            },
          },
        });

        if (!modifier) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye bulunamadı",
          });
        }

        return reply.send({
          success: true,
          data: modifier,
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Modifiye getirilirken bir hata oluştu",
        });
      }
    }
  );

  // Yeni modifiye oluştur
  fastify.post(
    "/",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const modifierData = createModifierSchema.parse(request.body);

        // Modifiye grup kontrolü
        const group = await prisma.modifierGroup.findFirst({
          where: {
            id: modifierData.groupId,
            deletedAt: null,
          },
        });

        if (!group) {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Geçersiz modifiye grup",
          });
        }

        // Envanter öğesi kontrolü (eğer belirtilmişse)
        if (modifierData.inventoryItemId) {
          const inventoryItem = await prisma.inventoryItem.findFirst({
            where: {
              id: modifierData.inventoryItemId,
              deletedAt: null,
              // Envanter öğesi company'ye ait olmalı
              OR: [
                { product: { companyId: user.companyId } },
                { productId: null }, // Raw material
              ],
            },
          });

          if (!inventoryItem) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Geçersiz envanter öğesi",
            });
          }
        }

        // Modifiye oluştur
        const modifier = await prisma.modifier.create({
          data: modifierData,
          include: {
            group: {
              select: {
                id: true,
                name: true,
                minSelection: true,
                maxSelection: true,
                required: true,
                active: true,
              },
            },
            inventoryItem: {
              select: {
                id: true,
                name: true,
                code: true,
                currentStock: true,
                unit: true,
              },
            },
          },
        });

        return reply.status(201).send({
          success: true,
          data: modifier,
          message: "Modifiye başarıyla oluşturuldu",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz modifiye bilgileri"
              : "Modifiye oluşturulurken bir hata oluştu",
        });
      }
    }
  );

  // Modifiye güncelle
  fastify.put(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params as { id: string };
        const modifierData = updateModifierSchema.parse(request.body);

        // Mevcut modifiye kontrolü
        const existingModifier = await prisma.modifier.findFirst({
          where: {
            id,
            deletedAt: null,
            group: {
              deletedAt: null,
            },
          },
        });

        if (!existingModifier) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye bulunamadı",
          });
        }

        // Envanter öğesi kontrolü (eğer belirtilmişse)
        if (modifierData.inventoryItemId) {
          const inventoryItem = await prisma.inventoryItem.findFirst({
            where: {
              id: modifierData.inventoryItemId,
              deletedAt: null,
              // Envanter öğesi company'ye ait olmalı
              OR: [
                { product: { companyId: user.companyId } },
                { productId: null }, // Raw material
              ],
            },
          });

          if (!inventoryItem) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Geçersiz envanter öğesi",
            });
          }
        }

        // Modifiye güncelle
        const modifier = await prisma.modifier.update({
          where: { id },
          data: modifierData,
          include: {
            group: {
              select: {
                id: true,
                name: true,
                minSelection: true,
                maxSelection: true,
                required: true,
                active: true,
              },
            },
            inventoryItem: {
              select: {
                id: true,
                name: true,
                code: true,
                currentStock: true,
                unit: true,
              },
            },
          },
        });

        return reply.send({
          success: true,
          data: modifier,
          message: "Modifiye başarıyla güncellendi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz modifiye bilgileri"
              : "Modifiye güncellenirken bir hata oluştu",
        });
      }
    }
  );

  // Modifiye sil (soft delete)
  fastify.delete(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };

        // Mevcut modifiye kontrolü
        const existingModifier = await prisma.modifier.findFirst({
          where: {
            id,
            deletedAt: null,
            group: {
              deletedAt: null,
            },
          },
        });

        if (!existingModifier) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye bulunamadı",
          });
        }

        // Soft delete
        await prisma.modifier.update({
          where: { id },
          data: { deletedAt: new Date() },
        });

        return reply.send({
          success: true,
          message: "Modifiye başarıyla silindi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Modifiye silinirken bir hata oluştu",
        });
      }
    }
  );

  // Modifiye durumunu değiştir (aktif/pasif)
  fastify.put(
    "/:id/toggle-status",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };

        // Mevcut modifiye kontrolü
        const existingModifier = await prisma.modifier.findFirst({
          where: {
            id,
            deletedAt: null,
            group: {
              deletedAt: null,
            },
          },
        });

        if (!existingModifier) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye bulunamadı",
          });
        }

        // Durumu değiştir
        const modifier = await prisma.modifier.update({
          where: { id },
          data: { active: !existingModifier.active },
          include: {
            group: {
              select: {
                id: true,
                name: true,
                minSelection: true,
                maxSelection: true,
                required: true,
                active: true,
              },
            },
            inventoryItem: {
              select: {
                id: true,
                name: true,
                code: true,
                currentStock: true,
                unit: true,
              },
            },
          },
        });

        return reply.send({
          success: true,
          data: modifier,
          message: "Modifiye durumu başarıyla güncellendi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Modifiye durumu güncellenirken bir hata oluştu",
        });
      }
    }
  );
}
