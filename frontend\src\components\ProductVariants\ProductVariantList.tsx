import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Edit, Trash2, MoreVertical, Eye, ToggleLeft, ToggleRight } from "lucide-react";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Badge } from "../ui/Badge";
import { DropdownMenu } from "../ui/DropdownMenu";
import { ConfirmDialog } from "../ui/ConfirmDialog";
import { formatCurrency, formatDate } from "../../lib/utils";
import type { ProductVariant } from "../../types/api";

interface ProductVariantListProps {
  variants: ProductVariant[];
  isLoading?: boolean;
  onEdit: (variant: ProductVariant) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string) => void;
  onView?: (variant: ProductVariant) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export const ProductVariantList: React.FC<ProductVariantListProps> = ({
  variants,
  isLoading = false,
  onEdit,
  onDelete,
  onToggleStatus,
  onView,
  searchQuery,
  onSearchChange,
}) => {
  const { t } = useTranslation();
  const [deleteConfirm, setDeleteConfirm] = useState<{
    isOpen: boolean;
    variantId: string;
    variantName: string;
  }>({
    isOpen: false,
    variantId: "",
    variantName: "",
  });

  const handleDeleteClick = (variant: ProductVariant) => {
    setDeleteConfirm({
      isOpen: true,
      variantId: variant.id,
      variantName: variant.name,
    });
  };

  const handleDeleteConfirm = () => {
    onDelete(deleteConfirm.variantId);
    setDeleteConfirm({ isOpen: false, variantId: "", variantName: "" });
  };

  const handleDeleteCancel = () => {
    setDeleteConfirm({ isOpen: false, variantId: "", variantName: "" });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, index) => (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 animate-pulse"
          >
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
              </div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (variants.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 dark:text-gray-500 text-lg mb-2">
          {searchQuery ? t("variants.noVariantsFound") : t("variants.noVariantsFound")}
        </div>
        {searchQuery && (
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            "{searchQuery}" için sonuç bulunamadı
          </p>
        )}
      </div>
    );
  }

  return (
    <>
      {/* Search */}
      <div className="mb-6">
        <Input
          placeholder={t("variants.searchVariants")}
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="bg-[#292C2D] dark:bg-[#292C2D] max-w-md"
        />
      </div>

      {/* Variant Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {variants.map((variant) => (
          <div
            key={variant.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                    {variant.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {variant.code}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={variant.active ? "success" : "secondary"}
                    size="sm"
                  >
                    {variant.active ? t("variants.active") : t("variants.inactive")}
                  </Badge>
                  <DropdownMenu
                    trigger={
                      <Button variant="ghost" size="sm">
                        <MoreVertical size={16} />
                      </Button>
                    }
                    items={[
                      ...(onView
                        ? [
                            {
                              label: t("common.view"),
                              icon: Eye,
                              onClick: () => onView(variant),
                            },
                          ]
                        : []),
                      {
                        label: t("common.edit"),
                        icon: Edit,
                        onClick: () => onEdit(variant),
                      },
                      {
                        label: variant.active
                          ? t("common.deactivate")
                          : t("common.activate"),
                        icon: variant.active ? ToggleLeft : ToggleRight,
                        onClick: () => onToggleStatus(variant.id),
                      },
                      {
                        label: t("common.delete"),
                        icon: Trash2,
                        onClick: () => handleDeleteClick(variant),
                        className: "text-red-600 dark:text-red-400",
                      },
                    ]}
                  />
                </div>
              </div>

              {/* Details */}
              <div className="space-y-3">
                {/* Price */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {t("variants.price")}:
                  </span>
                  <span className="text-lg font-semibold text-green-600 dark:text-green-400">
                    {formatCurrency(variant.price)}
                  </span>
                </div>

                {/* Cost Price */}
                {variant.costPrice && variant.costPrice > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("variants.costPrice")}:
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {formatCurrency(variant.costPrice)}
                    </span>
                  </div>
                )}

                {/* SKU */}
                {variant.sku && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("variants.sku")}:
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-mono">
                      {variant.sku}
                    </span>
                  </div>
                )}

                {/* Barcode */}
                {variant.barcode && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("variants.barcode")}:
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-mono">
                      {variant.barcode}
                    </span>
                  </div>
                )}

                {/* Product Info */}
                {variant.product && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {t("products.product")}:
                      </span>
                      <span className="text-sm text-gray-600 dark:text-gray-300">
                        {variant.product.name}
                      </span>
                    </div>
                  </div>
                )}

                {/* Created Date */}
                <div className="flex justify-between items-center text-xs text-gray-400 dark:text-gray-500">
                  <span>{t("variants.createdAt")}:</span>
                  <span>{formatDate(variant.createdAt)}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteConfirm.isOpen}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        title={t("variants.deleteConfirmation")}
        message={t("variants.confirmDelete")}
        confirmText={t("common.delete")}
        cancelText={t("common.cancel")}
        variant="danger"
      />
    </>
  );
};
