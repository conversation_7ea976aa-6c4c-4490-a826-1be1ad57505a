import { ArrowLeft, Plus } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  useCreateModifier,
  useDeleteModifier,
  useModifiersByGroup,
  useToggleModifierStatus,
  useUpdateModifier,
} from "../../hooks/useVariantsAndModifiers";
import type { Modifier, ModifierGroup } from "../../types/api";
import { Button } from "../ui/Button";
import { ModifierForm } from "./ModifierForm";
import { ModifierList } from "./ModifierList";

interface ModifierManagementProps {
  group: ModifierGroup;
  onBack: () => void;
}

export const ModifierManagement: React.FC<ModifierManagementProps> = ({
  group,
  onBack,
}) => {
  const { t } = useTranslation();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingModifier, setEditingModifier] = useState<
    Modifier | undefined
  >();
  const [searchQuery, setSearchQuery] = useState("");

  // API hooks
  const { data: modifiersResponse, isLoading } = useModifiersByGroup(group.id, {
    search: searchQuery || undefined,
    limit: 50,
  });

  const createModifierMutation = useCreateModifier();
  const updateModifierMutation = useUpdateModifier();
  const deleteModifierMutation = useDeleteModifier();
  const toggleStatusMutation = useToggleModifierStatus();

  const modifiers = modifiersResponse?.data?.data || [];

  const handleAddModifier = () => {
    setEditingModifier(undefined);
    setIsFormOpen(true);
  };

  const handleEditModifier = (modifier: Modifier) => {
    setEditingModifier(modifier);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingModifier) {
        await updateModifierMutation.mutateAsync({
          id: editingModifier.id,
          data,
        });
      } else {
        await createModifierMutation.mutateAsync({
          ...data,
          groupId: group.id,
        });
      }
      setIsFormOpen(false);
      setEditingModifier(undefined);
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  const handleDeleteModifier = async (id: string) => {
    try {
      await deleteModifierMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      await toggleStatusMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingModifier(undefined);
  };

  const isFormLoading =
    createModifierMutation.isPending || updateModifierMutation.isPending;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeft size={20} />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("modifiers.title")}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {group.name} - {t("modifiers.subtitle")}
            </p>
          </div>
        </div>
        <Button
          onClick={handleAddModifier}
          className="flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>{t("modifiers.addModifier")}</span>
        </Button>
      </div>

      {/* Group Info Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Min Seçim
            </p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {group.minSelection}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Max Seçim
            </p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {group.maxSelection}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">Ücretsiz</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {group.freeSelection}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">Durum</p>
            <p className="text-lg font-semibold">
              {group.required ? (
                <span className="text-yellow-600 dark:text-yellow-400">
                  Zorunlu
                </span>
              ) : (
                <span className="text-gray-600 dark:text-gray-400">
                  İsteğe Bağlı
                </span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Toplam Modifiye
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {modifiers.length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-blue-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Aktif Modifiye
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {modifiers.filter(m => m.active).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-green-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Ortalama Fiyat
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {modifiers.length > 0
                  ? `₺${(
                      modifiers.reduce((sum, m) => sum + m.price, 0) /
                      modifiers.length
                    ).toFixed(2)}`
                  : "₺0.00"}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-yellow-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Stoklu Modifiye
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {modifiers.filter(m => m.inventoryItem).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-purple-600 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Modifier List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="p-6">
          <ModifierList
            modifiers={modifiers}
            isLoading={isLoading}
            onEdit={handleEditModifier}
            onDelete={handleDeleteModifier}
            onToggleStatus={handleToggleStatus}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            showGroupInfo={false}
          />
        </div>
      </div>

      {/* Form Modal */}
      <ModifierForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        modifier={editingModifier}
        defaultGroupId={group.id}
        isLoading={isFormLoading}
      />
    </div>
  );
};
