import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Plus, ArrowLeft } from "lucide-react";
import { Button } from "../ui/Button";
import { ModifierGroupForm } from "./ModifierGroupForm";
import { ModifierGroupList } from "./ModifierGroupList";
import {
  useModifierGroups,
  useCreateModifierGroup,
  useUpdateModifierGroup,
  useDeleteModifierGroup,
  useToggleModifierGroupStatus,
} from "../../hooks/useVariantsAndModifiers";
import type { ModifierGroup } from "../../types/api";

interface ModifierGroupManagementProps {
  onBack: () => void;
  onManageModifiers?: (group: ModifierGroup) => void;
}

export const ModifierGroupManagement: React.FC<ModifierGroupManagementProps> = ({
  onBack,
  onManageModifiers,
}) => {
  const { t } = useTranslation();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<ModifierGroup | undefined>();
  const [searchQuery, setSearchQuery] = useState("");

  // API hooks
  const { data: groupsResponse, isLoading } = useModifierGroups({
    search: searchQuery || undefined,
    limit: 50,
  });

  const createGroupMutation = useCreateModifierGroup();
  const updateGroupMutation = useUpdateModifierGroup();
  const deleteGroupMutation = useDeleteModifierGroup();
  const toggleStatusMutation = useToggleModifierGroupStatus();

  const groups = groupsResponse?.data?.data || [];

  const handleAddGroup = () => {
    setEditingGroup(undefined);
    setIsFormOpen(true);
  };

  const handleEditGroup = (group: ModifierGroup) => {
    setEditingGroup(group);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingGroup) {
        await updateGroupMutation.mutateAsync({
          id: editingGroup.id,
          data,
        });
      } else {
        await createGroupMutation.mutateAsync(data);
      }
      setIsFormOpen(false);
      setEditingGroup(undefined);
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  const handleDeleteGroup = async (id: string) => {
    try {
      await deleteGroupMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      await toggleStatusMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingGroup(undefined);
  };

  const isFormLoading =
    createGroupMutation.isPending || updateGroupMutation.isPending;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeft size={20} />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("modifierGroups.title")}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t("modifierGroups.subtitle")}
            </p>
          </div>
        </div>
        <Button onClick={handleAddGroup} className="flex items-center space-x-2">
          <Plus size={20} />
          <span>{t("modifierGroups.addGroup")}</span>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Toplam Grup
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {groups.length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-blue-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Aktif Grup
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {groups.filter((g) => g.active).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-green-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Zorunlu Grup
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {groups.filter((g) => g.required).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-yellow-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Toplam Modifiye
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {groups.reduce((sum, g) => sum + (g._count?.modifiers || 0), 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-purple-600 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Group List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="p-6">
          <ModifierGroupList
            groups={groups}
            isLoading={isLoading}
            onEdit={handleEditGroup}
            onDelete={handleDeleteGroup}
            onToggleStatus={handleToggleStatus}
            onManageModifiers={onManageModifiers}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
        </div>
      </div>

      {/* Form Modal */}
      <ModifierGroupForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        group={editingGroup}
        isLoading={isFormLoading}
      />
    </div>
  );
};
