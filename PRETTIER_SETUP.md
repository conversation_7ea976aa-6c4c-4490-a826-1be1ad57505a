# Prettier Konfigürasyonu

Bu proje için Prettier kod formatlama aracı yapılandırılmıştır.

## Konfigürasyon Dosyaları

- `.prettierrc` - Ana Prettier konfigürasyonu
- `.prettierignore` - Formatlanmayacak dosya ve klasörler
- `.vscode/settings.json` - VS Code için otomatik formatlama ayarları
- `.vscode/extensions.json` - Önerilen VS Code eklentileri

## Prettier A<PERSON>ları

```json
{
  "semi": true,                    // Satır sonunda noktalı virgül
  "trailingComma": "es5",         // ES5 uyumlu trailing comma
  "singleQuote": false,           // Çift tırnak kullan
  "printWidth": 80,               // Maksimum satır uzunluğu
  "tabWidth": 2,                  // Tab genişliği
  "useTabs": false,               // <PERSON> kullan, tab değil
  "bracketSpacing": true,         // Obje parantezlerinde boşluk
  "bracketSameLine": false,       // JSX kapanış parantezi yeni satırda
  "arrowParens": "avoid",         // Arrow function'larda gereksiz parantez yok
  "endOfLine": "lf",              // Unix satır sonu
  "plugins": ["prettier-plugin-organize-imports"]  // Import'ları organize et
}
```

## Kullanım

### Tüm projeyi formatla
```bash
npm run format
```

### Format kontrolü yap
```bash
npm run format:check
```

### Sadece frontend'i formatla
```bash
npm run format:frontend
```

### Sadece backend'i formatla
```bash
npm run format:backend
```

## VS Code Entegrasyonu

VS Code kullanıyorsanız:

1. Prettier eklentisini yükleyin: `esbenp.prettier-vscode`
2. Dosya kaydederken otomatik formatlama aktif
3. Import'lar otomatik organize edilir
4. Kod kalitesi için önerilen eklentiler yüklenir

## Git Hook'ları (Opsiyonel)

Commit öncesi otomatik formatlama için husky ve lint-staged kullanabilirsiniz:

```bash
npm install --save-dev husky lint-staged
```

## Özellikler

- ✅ Tutarlı kod formatı
- ✅ Import'ları otomatik organize etme
- ✅ VS Code entegrasyonu
- ✅ Workspace ayarları
- ✅ Kapsamlı ignore listesi
- ✅ TypeScript/React/Node.js desteği
- ✅ Türkçe yorum desteği

## Dosya Türleri

Prettier aşağıdaki dosya türlerini formatlar:
- JavaScript (.js, .jsx)
- TypeScript (.ts, .tsx)
- JSON (.json)
- CSS/SCSS (.css, .scss)
- HTML (.html)
- Markdown (.md)

## Ignore Edilen Dosyalar

- node_modules/
- dist/, build/, coverage/
- .env dosyaları
- Log dosyaları
- IDE ayar dosyaları
- Package lock dosyaları
- Prisma migration dosyaları
